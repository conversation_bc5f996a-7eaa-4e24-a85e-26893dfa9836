npm test -- --testPathPattern="GovernanceRuleOrchestrationManager.test.ts" --verbose --testTimeout=10000

> oa-framework@1.0.0 test
> jest --testPathPattern=GovernanceRuleOrchestrationManager.test.ts --verbose --testTimeout=10000

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts (31.475 s, 69 MB heap size)
  GovernanceRuleOrchestrationManager
    Initialization and Lifecycle
      ✓ should initialize with default configuration (4 ms)
      ✓ should initialize resilient timing components correctly (3 ms)
      ✓ should initialize and shutdown properly (3 ms)
      ✕ should handle initialization errors gracefully (2 ms)
      ✓ should validate service name and version (2 ms)
    Orchestration Configuration
      ✓ should initialize orchestration with valid configuration (3 ms)
      ✓ should reject invalid orchestration configuration (23 ms)
      ✓ should handle configuration validation edge cases (3 ms)
    Workflow Execution
      ✕ should execute workflow successfully (3 ms)
      ✕ should handle workflow validation errors (4 ms)
      ✓ should handle step execution failures (3 ms)
      ✕ should handle different step types correctly (3 ms)
      ✕ should handle workflow with parallel executions (5003 ms)
    Multi-Rule Coordination
      ✕ should coordinate multi-rule execution successfully (5002 ms)
      ✕ should handle empty rules array (2 ms)
      ✓ should handle invalid coordination strategy (2 ms)
      ✕ should handle different coordination strategies (5003 ms)
      ✕ should handle rule execution failures with different failure handling strategies (12 ms)
    Rule Set Orchestration
      ✕ should orchestrate rule sets successfully (5006 ms)
      ✕ should handle circular dependencies in rule sets (5005 ms)
      ✓ should handle empty rule sets (9 ms)
      ✕ should handle rule sets with complex dependencies (5004 ms)
    Workflow Coordination
      ✕ should manage workflow coordination successfully (4 ms)
      ✕ should handle workflow validation errors (2 ms)
      ✓ should handle empty workflows array (2 ms)
      ✓ should handle workflow dependencies correctly (2 ms)
    Metrics and Monitoring
      ✕ should get orchestration metrics successfully (3 ms)
      ✕ should handle metrics collection errors (3 ms)
      ✓ should update health status based on metrics (3 ms)
      ✓ should perform health checks on services and orchestrations (3 ms)
    Memory Safety and Resilient Timing
      ✓ should validate MEM-SAFE-002 compliance (3 ms)
      ✕ should handle resilient timing context creation and cleanup (2 ms)
      ✓ should handle timing context errors gracefully (3 ms)
      ✕ should cleanup resources properly during shutdown (3 ms)
    Singleton Factory
      ✓ should return same instance on multiple calls (2 ms)
      ✓ should create new instance with configuration (1 ms)
    Branch Coverage Enhancement
      ✓ should test constructor test mode detection branches (2 ms)
      ✓ should test doInitialize test mode branches (2 ms)
      ✕ should test doShutdown test mode branches (3 ms)
      ✕ should test resilient timing cleanup branches (2 ms)
      ✕ should test resilient timing cleanup error branches (2 ms)
      ✓ should test monitoring setup branches in test mode (2 ms)
      ✕ should test orchestration configuration validation branches (3 ms)
      ✕ should test workflow validation edge cases (3 ms)
      ✕ should test private method branches with surgical precision (2 ms)
      ✕ should test workflow step execution branches (3 ms)
      ✓ should test error handling branches with strategic error injection (3 ms)
    Surgical Precision Coverage
      ✓ should test conditional branches in private methods (2 ms)
      ✓ should test ternary operator branches (3 ms)
      ✓ should test logical operator branches (&&, ||) (2 ms)
      ✓ should test switch statement branches (2 ms)
      ✓ should test error instanceof branches (3 ms)
    Error Handling and Edge Cases
      ✕ should handle initialization failure gracefully (3 ms)
      ✕ should handle shutdown errors gracefully (2 ms)
      ✓ should handle workflow failure scenarios (3 ms)
      ✓ should handle condition evaluation edge cases (2 ms)
      ✓ should handle resource calculation edge cases (3 ms)
      ✕ should handle metrics collection errors (2 ms)

  ● GovernanceRuleOrchestrationManager › Initialization and Lifecycle › should handle initialization errors gracefully

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      304 |       (prodManager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Init failed'));
      305 |
    > 306 |       await expect(prodManager.initialize()).rejects.toThrow('Init failed');
          |             ^
      307 |
      308 |       // Restore original method
      309 |       (prodManager as any).doInitialize = originalDoInitialize;

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:306:13)

  ● GovernanceRuleOrchestrationManager › Workflow Execution › should execute workflow successfully

    Service not found: governance-rule-engine

      1299 |
      1300 |       if (!service) {
    > 1301 |         throw new Error(`Service not found: ${serviceId}`);
           |               ^
      1302 |       }
      1303 |     }
      1304 |

      at GovernanceRuleOrchestrationManager._executeServiceCall (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1301:15)
      at GovernanceRuleOrchestrationManager._executeStep (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1210:20)
      at GovernanceRuleOrchestrationManager._executeWorkflowSteps (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1186:20)
      at GovernanceRuleOrchestrationManager.executeWorkflow (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:492:40)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:536:22)

  ● GovernanceRuleOrchestrationManager › Workflow Execution › should handle workflow validation errors

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    Expected: "workflow_execution", Any<Object>

    Number of calls: 0

      555 |
      556 |       await expect(manager.executeWorkflow(invalidWorkflow, sampleContext)).rejects.toThrow('Invalid workflow definition');
    > 557 |       expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_execution', expect.any(Object));
          |                                                 ^
      558 |     });
      559 |
      560 |     test('should handle step execution failures', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:557:49)

  ● GovernanceRuleOrchestrationManager › Workflow Execution › should handle different step types correctly

    Service not found: governance-rule-engine

      1299 |
      1300 |       if (!service) {
    > 1301 |         throw new Error(`Service not found: ${serviceId}`);
           |               ^
      1302 |       }
      1303 |     }
      1304 |

      at GovernanceRuleOrchestrationManager._executeServiceCall (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1301:15)
      at GovernanceRuleOrchestrationManager._executeStep (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1210:20)
      at GovernanceRuleOrchestrationManager._executeWorkflowSteps (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1186:20)
      at GovernanceRuleOrchestrationManager.executeWorkflow (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:492:40)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:620:22)

  ● GovernanceRuleOrchestrationManager › Workflow Execution › should handle workflow with parallel executions

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      622 |     });
      623 |
    > 624 |     test('should handle workflow with parallel executions', async () => {
          |     ^
      625 |       await manager.initialize();
      626 |
      627 |       const parallelWorkflow = {

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:624:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:440:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Multi-Rule Coordination › should coordinate multi-rule execution successfully

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      759 |     };
      760 |
    > 761 |     test('should coordinate multi-rule execution successfully', async () => {
          |     ^
      762 |       await manager.initialize();
      763 |
      764 |       const results = await manager.coordinateMultiRuleExecution(sampleRules, sampleStrategy);

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:761:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:667:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Multi-Rule Coordination › should handle empty rules array

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    Expected: "multi_rule_coordination", Any<Object>

    Number of calls: 0

      776 |
      777 |       await expect(manager.coordinateMultiRuleExecution([], sampleStrategy)).rejects.toThrow('No rules provided for coordination');
    > 778 |       expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('multi_rule_coordination', expect.any(Object));
          |                                                 ^
      779 |     });
      780 |
      781 |     test('should handle invalid coordination strategy', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:778:49)

  ● GovernanceRuleOrchestrationManager › Multi-Rule Coordination › should handle different coordination strategies

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      795 |     });
      796 |
    > 797 |     test('should handle different coordination strategies', async () => {
          |     ^
      798 |       await manager.initialize();
      799 |
      800 |       // Test peer-to-peer strategy (asynchronous)

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:797:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:667:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Multi-Rule Coordination › should handle rule execution failures with different failure handling strategies

    TypeError: Cannot read properties of undefined (reading 'result')

      1495 |   ): Promise<void> {
      1496 |     // Update metrics based on results
    > 1497 |     const successCount = results.filter(r => r.result.success).length;
           |                                                ^
      1498 |     const failureCount = results.length - successCount;
      1499 |
      1500 |     this._orchestrationMetrics.totalWorkflows += results.length;

      at server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1497:48
          at Array.filter (<anonymous>)
      at GovernanceRuleOrchestrationManager._handleCoordinationResults (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1497:34)
      at GovernanceRuleOrchestrationManager.coordinateMultiRuleExecution (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:577:18)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:857:23)

  ● GovernanceRuleOrchestrationManager › Rule Set Orchestration › should orchestrate rule sets successfully

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1057 |     };
      1058 |
    > 1059 |     test('should orchestrate rule sets successfully', async () => {
           |     ^
      1060 |       await manager.initialize();
      1061 |
      1062 |       const result = await manager.orchestrateRuleSets(sampleRuleSets, sampleExecutionContext);

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1059:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:869:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Rule Set Orchestration › should handle circular dependencies in rule sets

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1070 |     });
      1071 |
    > 1072 |     test('should handle circular dependencies in rule sets', async () => {
           |     ^
      1073 |       await manager.initialize();
      1074 |
      1075 |       const circularRuleSets = [

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1072:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:869:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Rule Set Orchestration › should handle rule sets with complex dependencies

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1105 |     });
      1106 |
    > 1107 |     test('should handle rule sets with complex dependencies', async () => {
           |     ^
      1108 |       await manager.initialize();
      1109 |
      1110 |       const complexRuleSets = [

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1107:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:869:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:147:1)

  ● GovernanceRuleOrchestrationManager › Workflow Coordination › should manage workflow coordination successfully

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

      1234 |       expect(results[0].workflowId).toBe('workflow-001');
      1235 |       expect(results[1].workflowId).toBe('workflow-002');
    > 1236 |       expect(mockResilientTimer.start).toHaveBeenCalled();
           |                                        ^
      1237 |       expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_coordination', expect.any(Object));
      1238 |     });
      1239 |

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1236:40)

  ● GovernanceRuleOrchestrationManager › Workflow Coordination › should handle workflow validation errors

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    Expected: "workflow_coordination", Any<Object>

    Number of calls: 0

      1250 |
      1251 |       await expect(manager.manageWorkflowCoordination(invalidWorkflows)).rejects.toThrow();
    > 1252 |       expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_coordination', expect.any(Object));
           |                                                 ^
      1253 |     });
      1254 |
      1255 |     test('should handle empty workflows array', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1252:49)

  ● GovernanceRuleOrchestrationManager › Metrics and Monitoring › should get orchestration metrics successfully

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

      1297 |       expect(metrics.metrics).toBeDefined();
      1298 |       expect(metrics.health).toBeDefined();
    > 1299 |       expect(mockResilientTimer.start).toHaveBeenCalled();
           |                                        ^
      1300 |       expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('metrics_collection', expect.any(Object));
      1301 |     });
      1302 |

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1299:40)

  ● GovernanceRuleOrchestrationManager › Metrics and Monitoring › should handle metrics collection errors

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    Expected: "metrics_collection", Any<Object>

    Number of calls: 0

      1309 |
      1310 |       await expect(manager.getOrchestrationMetrics()).rejects.toThrow('Metrics collection failed');
    > 1311 |       expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('metrics_collection', expect.any(Object));
           |                                                 ^
      1312 |
      1313 |       // Restore original method
      1314 |       (manager as any)._collectCurrentMetrics = originalCollectCurrentMetrics;

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1311:49)

  ● GovernanceRuleOrchestrationManager › Memory Safety and Resilient Timing › should handle resilient timing context creation and cleanup

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

      1388 |
      1389 |       // Test timing context creation
    > 1390 |       expect(mockResilientTimer.start).toHaveBeenCalled();
           |                                        ^
      1391 |
      1392 |       // Test metrics recording
      1393 |       expect(mockMetricsCollector.recordTiming).toHaveBeenCalled();

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1390:40)

  ● GovernanceRuleOrchestrationManager › Memory Safety and Resilient Timing › should cleanup resources properly during shutdown

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      1477 |
      1478 |       // Verify cleanup
    > 1479 |       expect((manager as any)._activeOrchestrations.size).toBe(0);
           |                                                           ^
      1480 |       expect((manager as any)._workflowRegistry.size).toBe(0);
      1481 |       expect((manager as any)._coordinationStrategies.size).toBe(0);
      1482 |       expect((manager as any)._serviceRegistry.size).toBe(0);

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1479:59)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test doShutdown test mode branches

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      1567 |
      1568 |       // Verify test mode immediate cleanup
    > 1569 |       expect((manager as any)._activeOrchestrations.size).toBe(0);
           |                                                           ^
      1570 |       expect((manager as any)._workflowRegistry.size).toBe(0);
      1571 |       expect((manager as any)._isInitialized).toBe(false);
      1572 |     });

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1569:59)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test resilient timing cleanup branches

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

      1600 |
      1601 |       // Verify cleanup was called
    > 1602 |       expect(mockTimerWithCleanup.cleanup).toHaveBeenCalled();
           |                                            ^
      1603 |       expect(mockCollectorWithCleanup.cleanup).toHaveBeenCalled();
      1604 |     });
      1605 |

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1602:44)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test resilient timing cleanup error branches

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

      1632 |       // Should not throw, just log error
      1633 |       await expect(prodManager.shutdown()).resolves.not.toThrow();
    > 1634 |       expect(mockTimerWithError.cleanup).toHaveBeenCalled();
           |                                          ^
      1635 |     });
      1636 |
      1637 |     test('should test monitoring setup branches in test mode', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1634:42)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test orchestration configuration validation branches

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      1707 |       // Test invalid priority branch - test with a completely invalid structure
      1708 |       const invalidPriorityConfig = { ...validConfig, priority: undefined as any };
    > 1709 |       await expect(manager.initializeOrchestration(invalidPriorityConfig)).rejects.toThrow();
           |             ^
      1710 |
      1711 |       // Test negative timeout branch
      1712 |       const negativeTimeoutConfig = {

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1709:13)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test workflow validation edge cases

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: {"executedSteps": [{"action": {"method": "test", "parameters": {}, "service": "test", "type": "test"}, "dependencies": [], "id": "invalid-step", "name": "Invalid Step", "parameters": {}, "retryPolicy": {"backoffStrategy": "linear", "delay": 10, "maxAttempts": 1, "maxDelay": 50}, "timeout": 100, "type": "INVALID", "validation": {"enabled": false, "onFailure": "continue", "rules": []}}], "orchestrationId": "orchestration-1756753570218-ydintirn9", "parallelExecutions": [], "resourceUsage": {"cpu": 25.680610346883135, "memory": 937.680343462956, "network": 88.57634407844482, "storage": 85.69165860338398}, "status": "COMPLETED", "synchronizationPoints": [], "workflowId": "empty-workflow"}

      1776 |       };
      1777 |
    > 1778 |       await expect(manager.executeWorkflow(invalidStepWorkflow, sampleContext)).rejects.toThrow();
           |             ^
      1779 |     });
      1780 |
      1781 |     test('should test private method branches with surgical precision', async () => {

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1778:13)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test private method branches with surgical precision

    TypeError: this.generateId is not a function

      1010 |       errors.push('Invalid orchestration configuration: config must be a valid object');
      1011 |       return {
    > 1012 |         validationId: this.generateId(),
           |                            ^
      1013 |         componentId: 'governance-rule-orchestration-manager',
      1014 |         timestamp: new Date(),
      1015 |         executionTime: 0,

      at GovernanceRuleOrchestrationManager._validateOrchestrationConfig (server/src/platform/governance/advanced-management/GovernanceRuleOrchestrationManager.ts:1012:28)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1788:32)

  ● GovernanceRuleOrchestrationManager › Branch Coverage Enhancement › should test workflow step execution branches

    expect(received).resolves.toBeDefined()

    Received promise rejected instead of resolved
    Rejected to value: [Error: Service not found: test]

      1835 |       };
      1836 |
    > 1837 |       await expect(executeStep(actionStep, sampleContext)).resolves.toBeDefined();
           |             ^
      1838 |
      1839 |       // Test CONDITION step type
      1840 |       const conditionStep = {

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:1837:13)

  ● GovernanceRuleOrchestrationManager › Error Handling and Edge Cases › should handle initialization failure gracefully

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      2079 |       (manager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Initialization failed'));
      2080 |
    > 2081 |       await expect(manager.initialize()).rejects.toThrow('Initialization failed');
           |             ^
      2082 |
      2083 |       // Restore original method
      2084 |       (manager as any).doInitialize = originalDoInitialize;

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:2081:13)

  ● GovernanceRuleOrchestrationManager › Error Handling and Edge Cases › should handle shutdown errors gracefully

    expect(received).rejects.toThrow()

    Received promise resolved instead of rejected
    Resolved to value: undefined

      2091 |       (manager as any)._cancelActiveOrchestrations = jest.fn().mockRejectedValue(new Error('Shutdown error'));
      2092 |
    > 2093 |       await expect(manager.shutdown()).rejects.toThrow('Shutdown error');
           |             ^
      2094 |
      2095 |       // Restore original method
      2096 |       (manager as any)._cancelActiveOrchestrations = originalCancelActiveOrchestrations;

      at expect (node_modules/expect/build/index.js:113:15)
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:2093:13)

  ● GovernanceRuleOrchestrationManager › Error Handling and Edge Cases › should handle metrics collection errors

    Metrics error

      2211 |       // Mock performance metrics collection to fail
      2212 |       const originalCollectPerformanceMetrics = (manager as any)._collectPerformanceMetrics;
    > 2213 |       (manager as any)._collectPerformanceMetrics = jest.fn().mockRejectedValue(new Error('Metrics error'));
           |                                                                                 ^
      2214 |
      2215 |       // Should not throw error, just log it
      2216 |       await (manager as any)._collectPerformanceMetrics();

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts:2213:81)

Test Suites: 1 failed, 1 total
Tests:       28 failed, 30 passed, 58 total
Snapshots:   0 total
Time:        31.746 s
Ran all test suites matching /GovernanceRuleOrchestrationManager.test.ts/i.
