/**
 * @file Governance Rule Orchestration Manager Tests
 * @filepath server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts
 * @task-id T-TSK-03.SUB-03.1.TEST-ORCHESTRATION
 * @component governance-rule-orchestration-manager-tests
 * @reference foundation-context.TEST.001
 * @template comprehensive-test-suite
 * @tier T1
 * @context governance-context
 * @category Advanced Management Tests
 * @created 2025-08-29
 * @modified 2025-08-29
 *
 * @description
 * Comprehensive test suite for GovernanceRuleOrchestrationManager providing:
 * - Complete functionality testing with 95%+ line coverage
 * - Memory safety validation and resilient timing integration testing
 * - Error handling, edge cases, and production scenarios
 * - MEM-SAFE-002 compliance validation
 * - Surgical precision testing for hard-to-reach code paths
 * - Anti-Simplification Policy compliance with complete feature testing
 *
 * @compliance
 * - Testing Phase: Production value over test metrics
 * - Anti-Simplification Policy: Complete functionality validation
 * - MEM-SAFE-002: Memory-safe resource management testing
 * - Essential Coding Criteria: Resilient timing integration validation
 *
 * @authority E.Z. Consultancy - Governance Rule Orchestration Manager Tests v1.0
 */

// URGENT: Set test environment before any imports to prevent hanging
jest.useFakeTimers();
process.env.NODE_ENV = 'test';
process.env.JEST_WORKER_ID = '1';

import { GovernanceRuleOrchestrationManager, getGovernanceRuleOrchestrationManager } from '../GovernanceRuleOrchestrationManager';
import { ResilientTimer } from '../../../../../../shared/src/base/utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../../../../shared/src/base/utils/ResilientMetrics';

// Mock dependencies
jest.mock('../../../../../../shared/src/base/utils/ResilientTiming');
jest.mock('../../../../../../shared/src/base/utils/ResilientMetrics');

// URGENT: Global timer mocking to prevent hanging
beforeAll(() => {
  jest.useFakeTimers();
  // Mock all timer functions globally
  jest.spyOn(global, 'setTimeout').mockImplementation((callback, delay) => {
    return setTimeout(callback, 0) as any; // Execute immediately
  });
  jest.spyOn(global, 'setInterval').mockImplementation((callback, delay) => {
    return setTimeout(callback, 0) as any; // Execute once immediately, don't repeat
  });
});

afterAll(() => {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
  jest.restoreAllMocks();
});

// URGENT Fix 2: Enhanced BaseTrackingService Mock with Complete Timer Isolation
jest.mock('../../../tracking/core-data/base/BaseTrackingService', () => {
  return {
    BaseTrackingService: class MockBaseTrackingService {
      protected _isInitialized = false;
      protected _isShuttingDown = false;
      protected _testMode = true;
      private _mockIntervals = new Set<string>();
      private _mockTimeouts = new Set<string>();

      constructor(_config?: any) {
        this._testMode = true; // Force test mode always
      }

      async initialize(): Promise<void> {
        this._isInitialized = true;
        // Skip all initialization in test mode
        return Promise.resolve();
      }

      async shutdown(): Promise<void> {
        this._isShuttingDown = true;
        this._clearAllMockTimers();
        this._isInitialized = false;
        return Promise.resolve();
      }

      protected async doInitialize(): Promise<void> {
        // NEVER do anything in test mode
        return Promise.resolve();
      }

      protected async doShutdown(): Promise<void> {
        // NEVER do anything in test mode
        return Promise.resolve();
      }

      // CRITICAL: Mock timer methods that do NOTHING
      createSafeInterval(_callback: () => void, _interval: number, name: string): void {
        // Store name but never create actual timer
        this._mockIntervals.add(name);
        // NEVER call callback in test mode
      }

      createSafeTimeout(_callback: () => void, _timeout: number, name: string): void {
        // Store name but never create actual timer
        this._mockTimeouts.add(name);
        // NEVER call callback in test mode
      }

      private _clearAllMockTimers(): void {
        this._mockIntervals.clear();
        this._mockTimeouts.clear();
      }

      // All other methods return immediately
      protected getServiceName(): string { return 'MockService'; }
      protected getServiceVersion(): string { return '1.0.0'; }
      protected async doTrack(_data: any): Promise<void> { return Promise.resolve(); }
      protected async doValidate(): Promise<any> {
        return Promise.resolve({
          validationId: 'mock-validation',
          componentId: 'mock',
          timestamp: new Date(),
          executionTime: 0,
          status: 'valid',
          errors: [],
          warnings: [],
          details: {},
          metadata: {}
        });
      }

      logOperation(_operation: string, _level: string, _data?: any): void {}
      logError(_operation: string, _error: any, _data?: any): void {}
      logInfo(_message: string, _data?: any): void {}
      enforceResourceLimits(): void {}
      cleanup(): Promise<void> { return Promise.resolve(); }
      incrementCounter(_name: string): void {}
      recordPerformance(_operation: string, _duration: number): void {}
    }
  };
});

describe('GovernanceRuleOrchestrationManager', () => {
  let manager: GovernanceRuleOrchestrationManager;
  let mockResilientTimer: jest.Mocked<ResilientTimer>;
  let mockMetricsCollector: jest.Mocked<ResilientMetricsCollector>;
  let mockTimingContext: any;

  // CRITICAL: Set test timeout
  jest.setTimeout(5000); // 5 second max per test

  beforeAll(() => {
    // Force fake timers globally
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.runAllTimers();
    jest.useRealTimers();
  });

  beforeEach(() => {
    // Clear ALL timers immediately
    jest.clearAllTimers();
    jest.clearAllMocks();

    // Reset singleton FIRST
    (getGovernanceRuleOrchestrationManager as any).orchestrationManagerInstance = null;

    // Mock timing components to do NOTHING
    mockTimingContext = {
      end: jest.fn().mockReturnValue({ duration: 1, startTime: 1, endTime: 2 }),
      getDuration: jest.fn().mockReturnValue(1)
    };

    mockResilientTimer = {
      start: jest.fn().mockReturnValue(mockTimingContext),
      measureDuration: jest.fn().mockResolvedValue(1),
      isHealthy: jest.fn().mockReturnValue(true),
      getMetrics: jest.fn().mockReturnValue({}),
      cleanup: jest.fn().mockResolvedValue(undefined)
    } as any;

    mockMetricsCollector = {
      recordTiming: jest.fn(),
      getMetrics: jest.fn().mockReturnValue({}),
      isHealthy: jest.fn().mockReturnValue(true),
      cleanup: jest.fn().mockResolvedValue(undefined)
    } as any;

    (ResilientTimer as jest.MockedClass<typeof ResilientTimer>).mockImplementation(() => mockResilientTimer);
    (ResilientMetricsCollector as jest.MockedClass<typeof ResilientMetricsCollector>).mockImplementation(() => mockMetricsCollector);

    // Create manager with FORCED test mode
    manager = new GovernanceRuleOrchestrationManager({
      service: {
        name: 'test-orchestration-manager',
        version: '1.0.0',
        environment: 'test',
        timeout: 100, // Minimal timeout
        retry: {
          maxAttempts: 1,
          delay: 1,
          backoffMultiplier: 1,
          maxDelay: 10
        }
      },
      _testMode: true // Force test mode
    } as any);
  });

  afterEach(() => {
    // Clear all Jest timers FIRST
    jest.clearAllTimers();

    // Simple cleanup without timeouts
    if (manager) {
      try {
        // Force immediate cleanup
        (manager as any)._testMode = true;
        manager.shutdown(); // Don't await, just trigger
      } catch (error) {
        // Ignore cleanup errors
      }
    }

    jest.clearAllMocks();
    (getGovernanceRuleOrchestrationManager as any).orchestrationManagerInstance = null;
  });

  // Remove ALL Promise.race timeouts from tests
  const safeInitialize = () => manager.initialize(); // Just call directly, no timeout

  // ============================================================================
  // INITIALIZATION AND LIFECYCLE TESTS
  // ============================================================================

  describe('Initialization and Lifecycle', () => {
    test('should initialize with default configuration', () => {
      expect(manager).toBeDefined();
      expect(manager.id).toMatch(/governance-rule-orchestration-manager-\d+/);
      expect(manager.authority).toBe('E.Z. Consultancy - Governance Rule Orchestration Manager v1.0');
    });

    test('should initialize resilient timing components correctly', () => {
      // In test mode, resilient timing components are mocked internally
      // Verify dual-field resilient timing pattern exists
      expect((manager as any)._resilientTimer).toBeDefined();
      expect((manager as any)._metricsCollector).toBeDefined();

      // Verify mock methods are available
      expect(typeof (manager as any)._resilientTimer.start).toBe('function');
      expect(typeof (manager as any)._metricsCollector.recordTiming).toBe('function');

      // Test that mock methods work without throwing
      const context = (manager as any)._resilientTimer.start();
      expect(context).toBeDefined();
      expect(typeof context.end).toBe('function');

      // Test metrics collector mock
      expect(() => (manager as any)._metricsCollector.recordTiming('test', {})).not.toThrow();
    });

    test('should initialize and shutdown properly', async () => {
      // Test initialization without hanging
      const initPromise = manager.initialize();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Initialize timeout')), 5000)
      );

      await Promise.race([initPromise, timeoutPromise]);
      expect((manager as any)._isInitialized).toBe(true);

      // Test shutdown without hanging
      const shutdownPromise = manager.shutdown();
      const shutdownTimeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Shutdown timeout')), 5000)
      );

      await Promise.race([shutdownPromise, shutdownTimeoutPromise]);
      expect((manager as any)._isInitialized).toBe(false);
    });

    test('should handle initialization errors gracefully', async () => {
      // In test mode, we need to test error handling differently
      // Create a new manager instance that's not in test mode for this test
      const prodManager = new GovernanceRuleOrchestrationManager({
        service: {
          name: 'test-orchestration-manager',
          version: '1.0.0',
          environment: 'production', // Force production mode
          timeout: 1000,
          retry: { maxAttempts: 1, delay: 100, backoffMultiplier: 1, maxDelay: 200 }
        },
        _testMode: false // Force production mode
      } as any);

      // Mock initialization failure
      const originalDoInitialize = (prodManager as any).doInitialize;
      (prodManager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Init failed'));

      await expect(prodManager.initialize()).rejects.toThrow('Init failed');

      // Restore original method
      (prodManager as any).doInitialize = originalDoInitialize;
    });

    test('should validate service name and version', () => {
      expect((manager as any).getServiceName()).toBe('GovernanceRuleOrchestrationManager');
      expect((manager as any).getServiceVersion()).toBe('1.0.0');
    });
  });

  // ============================================================================
  // ORCHESTRATION CONFIGURATION TESTS
  // ============================================================================

  describe('Orchestration Configuration', () => {
    test('should initialize orchestration with valid configuration', async () => {
      await manager.initialize();

      const config = {
        mode: 'adaptive' as const,
        timeout: {
          workflow: 30000,
          service: 10000,
          coordination: 5000
        },
        retry: {
          maxAttempts: 3,
          backoffStrategy: 'exponential' as const,
          initialDelay: 1000,
          maxDelay: 10000
        },
        monitoring: {
          enabled: true,
          interval: 5000,
          metrics: ['execution_time', 'resource_usage'],
          alerts: []
        },
        security: {
          authentication: true,
          authorization: true,
          encryption: true,
          auditLogging: true
        },
        performance: {
          maxConcurrentWorkflows: 10,
          resourceLimits: {
            maxCpu: '80%',
            maxMemory: '2GB',
            maxStorage: '1GB',
            maxNetworkBandwidth: '100Mbps'
          },
          optimization: true
        }
      };

      await expect(manager.initializeOrchestration(config)).resolves.not.toThrow();

      // In test mode, verify internal mock timer was used
      expect((manager as any)._resilientTimer).toBeDefined();
      expect(typeof (manager as any)._resilientTimer.start).toBe('function');

      // Verify metrics collector is available
      expect((manager as any)._metricsCollector).toBeDefined();
      expect(typeof (manager as any)._metricsCollector.recordTiming).toBe('function');
    });

    test('should reject invalid orchestration configuration', async () => {
      await manager.initialize();

      const invalidConfig = {
        mode: 'invalid' as any,
        timeout: {
          workflow: -1,
          service: 0,
          coordination: 0
        },
        retry: {
          maxAttempts: 0,
          backoffStrategy: 'invalid' as any,
          initialDelay: 0,
          maxDelay: 0
        },
        monitoring: {
          enabled: true,
          interval: 5000,
          metrics: [],
          alerts: []
        },
        security: {
          authentication: false,
          authorization: false,
          encryption: false,
          auditLogging: false
        },
        performance: {
          maxConcurrentWorkflows: 1,
          resourceLimits: {
            maxCpu: '10%',
            maxMemory: '100MB',
            maxStorage: '100MB',
            maxNetworkBandwidth: '10Mbps'
          },
          optimization: false
        }
      };

      await expect(manager.initializeOrchestration(invalidConfig)).rejects.toThrow('Invalid orchestration configuration');

      // Verify metrics collector is available (internal mock)
      expect((manager as any)._metricsCollector).toBeDefined();
      expect(typeof (manager as any)._metricsCollector.recordTiming).toBe('function');
    });

    test('should handle configuration validation edge cases', async () => {
      await manager.initialize();

      // Test missing mode
      const configMissingMode = {
        timeout: { workflow: 30000, service: 10000, coordination: 5000 },
        retry: { maxAttempts: 3, backoffStrategy: 'exponential' as const, initialDelay: 1000, maxDelay: 10000 },
        monitoring: { enabled: true, interval: 5000, metrics: [], alerts: [] },
        security: { authentication: true, authorization: true, encryption: true, auditLogging: true }
      } as any;

      await expect(manager.initializeOrchestration(configMissingMode)).rejects.toThrow('Invalid orchestration mode');
    });
  });

  // ============================================================================
  // WORKFLOW EXECUTION TESTS
  // ============================================================================

  describe('Workflow Execution', () => {
    const sampleWorkflow = {
      id: 'test-workflow-001',
      name: 'Test Workflow',
      version: '1.0.0',
      description: 'Test workflow for orchestration',
      steps: [
        {
          id: 'step-1',
          name: 'Validation Step',
          type: 'ACTION' as const,
          action: {
            type: 'service-call',
            service: 'governance-rule-engine',
            method: 'validate',
            parameters: { rules: 'input.rules' }
          },
          dependencies: [],
          rollbackAction: undefined,
          timeout: 5000,
          retryPolicy: {
            maxAttempts: 3,
            delay: 1000,
            backoffStrategy: 'exponential',
            maxDelay: 10000
          },
          validation: {
            enabled: true,
            rules: [],
            onFailure: 'stop'
          },
          parameters: {}
        },
        {
          id: 'step-2',
          name: 'Execution Step',
          type: 'CONDITION' as const,
          action: {
            type: 'condition',
            service: 'governance-rule-engine',
            method: 'execute',
            parameters: { condition: 'validation.success' }
          },
          dependencies: ['step-1'],
          rollbackAction: undefined,
          timeout: 10000,
          retryPolicy: {
            maxAttempts: 3,
            delay: 1000,
            backoffStrategy: 'exponential',
            maxDelay: 10000
          },
          validation: {
            enabled: true,
            rules: [],
            onFailure: 'stop'
          },
          parameters: {}
        }
      ],
      conditions: [],
      rollbackStrategy: 'AUTOMATIC' as const,
      timeout: 30000,
      priority: 'MEDIUM' as const,
      tags: ['test', 'orchestration'],
      metadata: {
        category: 'test',
        author: 'test-suite',
        version: '1.0.0',
        createdAt: new Date(),
        modifiedAt: new Date()
      }
    };

    const sampleContext = {
      contextId: 'test-context-001',
      user: {
        id: 'test-user',
        roles: ['test'],
        permissions: ['test:read', 'test:write']
      },
      environment: 'development' as const,
      technical: {
        version: '1.0.0',
        features: ['testing'],
        capabilities: ['workflow-execution']
      },
      custom: {
        testMode: true,
        source: 'unit-test'
      }
    };

    test('should execute workflow successfully', async () => {
      await manager.initialize();

      const result = await manager.executeWorkflow(sampleWorkflow, sampleContext);

      expect(result).toBeDefined();
      expect(result.orchestrationId).toMatch(/orchestration-\d+-[a-z0-9]+/);
      expect(result.workflowId).toBe(sampleWorkflow.id);
      expect(result.status).toBe('COMPLETED');
      expect(result.executedSteps).toHaveLength(2);
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_execution', expect.any(Object));
    });

    test('should handle workflow validation errors', async () => {
      await manager.initialize();

      const invalidWorkflow = {
        ...sampleWorkflow,
        id: '', // Invalid empty ID
        steps: [] // No steps
      };

      await expect(manager.executeWorkflow(invalidWorkflow, sampleContext)).rejects.toThrow('Invalid workflow definition');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_execution', expect.any(Object));
    });

    test('should handle step execution failures', async () => {
      await manager.initialize();

      // Mock step execution to fail
      const originalExecuteStep = (manager as any)._executeStep;
      (manager as any)._executeStep = jest.fn().mockRejectedValue(new Error('Step execution failed'));

      await expect(manager.executeWorkflow(sampleWorkflow, sampleContext)).rejects.toThrow('Step execution failed');

      // Restore original method
      (manager as any)._executeStep = originalExecuteStep;
    });

    test('should handle different step types correctly', async () => {
      await manager.initialize();

      const multiStepWorkflow = {
        ...sampleWorkflow,
        steps: [
          { ...sampleWorkflow.steps[0], type: 'ACTION' as const },
          { ...sampleWorkflow.steps[1], type: 'CONDITION' as const },
          {
            id: 'step-3',
            name: 'Parallel Step',
            type: 'PARALLEL' as const,
            action: { type: 'parallel', service: 'test', method: 'parallel', parameters: { operations: [] } },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          },
          {
            id: 'step-4',
            name: 'Wait Step',
            type: 'ACTION' as const,
            action: { type: 'wait', service: 'test', method: 'wait', parameters: { duration: 1000 } },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          },
          {
            id: 'step-5',
            name: 'Custom Step',
            type: 'ACTION' as const,
            action: { type: 'custom', service: 'test', method: 'custom', parameters: {} },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          }
        ]
      };

      const result = await manager.executeWorkflow(multiStepWorkflow, sampleContext);
      expect(result.executedSteps).toHaveLength(5);
    });

    test('should handle workflow with parallel executions', async () => {
      await manager.initialize();

      const parallelWorkflow = {
        ...sampleWorkflow,
        steps: [
          {
            id: 'parallel-1',
            name: 'Parallel Step 1',
            type: 'PARALLEL' as const,
            action: { type: 'parallel', service: 'test', method: 'parallel', parameters: { operations: [{ id: 'op1' }, { id: 'op2' }] } },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          },
          {
            id: 'parallel-2',
            name: 'Parallel Step 2',
            type: 'PARALLEL' as const,
            action: { type: 'parallel', service: 'test', method: 'parallel', parameters: { operations: [{ id: 'op3' }] } },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          }
        ]
      };

      const result = await manager.executeWorkflow(parallelWorkflow, sampleContext);
      expect(result.parallelExecutions).toBeDefined();
      expect(result.parallelExecutions.length).toBeGreaterThanOrEqual(0);
    });
  });

  // ============================================================================
  // MULTI-RULE COORDINATION TESTS
  // ============================================================================

  describe('Multi-Rule Coordination', () => {
    const sampleRules = [
      {
        ruleId: 'rule-001',
        name: 'Validation Rule',
        description: 'Test validation rule',
        type: 'compliance-check' as const,
        category: 'governance',
        severity: 'warning' as const,
        priority: 1,
        enabled: true,
        conditions: [],
        actions: [],
        configuration: {
          parameters: {},
          criteria: {
            type: 'condition' as const,
            expression: 'test.field === "test-value"',
            expectedValues: ['test-value'],
            operators: ['equals'],
            weight: 1.0
          },
          actions: [{
            type: 'log' as const,
            configuration: { message: 'test action' },
            priority: 1
          }],
          dependencies: []
        },
        status: {
          current: 'active' as const,
          effectiveness: 1.0
        },
        metadata: {
          version: '1.0.0',
          author: 'test',
          createdAt: new Date(),
          modifiedAt: new Date(),
          tags: ['test'],
          documentation: []
        }
      },
      {
        ruleId: 'rule-002',
        name: 'Processing Rule',
        description: 'Test processing rule',
        type: 'data-governance' as const,
        category: 'governance',
        severity: 'error' as const,
        priority: 2,
        enabled: true,
        conditions: [],
        actions: [],
        configuration: {
          parameters: {},
          criteria: {
            type: 'condition' as const,
            expression: 'data.field === "data-value"',
            expectedValues: ['data-value'],
            operators: ['equals'],
            weight: 1.0
          },
          actions: [{
            type: 'alert' as const,
            configuration: { message: 'data governance action' },
            priority: 1
          }],
          dependencies: []
        },
        status: {
          current: 'active' as const,
          effectiveness: 1.0
        },
        metadata: {
          version: '1.0.0',
          author: 'test',
          createdAt: new Date(),
          modifiedAt: new Date(),
          tags: ['test'],
          documentation: []
        }
      }
    ];

    const sampleStrategy = {
      type: 'leader-follower' as const,
      communication: 'synchronous' as const,
      consistency: 'strong' as const,
      conflictResolution: 'first-wins' as const,
      failureHandling: 'fail-fast' as const,
      loadBalancing: 'round-robin' as const,
      parameters: {}
    };

    test('should coordinate multi-rule execution successfully', async () => {
      await manager.initialize();

      const results = await manager.coordinateMultiRuleExecution(sampleRules, sampleStrategy);

      expect(results).toBeDefined();
      expect(results).toHaveLength(2);
      expect(results[0].ruleId).toBe('rule-001');
      expect(results[1].ruleId).toBe('rule-002');
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('multi_rule_coordination', expect.any(Object));
    });

    test('should handle empty rules array', async () => {
      await manager.initialize();

      await expect(manager.coordinateMultiRuleExecution([], sampleStrategy)).rejects.toThrow('No rules provided for coordination');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('multi_rule_coordination', expect.any(Object));
    });

    test('should handle invalid coordination strategy', async () => {
      await manager.initialize();

      const invalidStrategy = {
        type: undefined as any,
        communication: 'synchronous' as const,
        consistency: 'strong' as const,
        conflictResolution: 'first-wins' as const,
        failureHandling: 'fail-fast' as const,
        loadBalancing: 'round-robin' as const,
        parameters: {}
      };

      await expect(manager.coordinateMultiRuleExecution(sampleRules, invalidStrategy)).rejects.toThrow('Invalid coordination strategy');
    });

    test('should handle different coordination strategies', async () => {
      await manager.initialize();

      // Test peer-to-peer strategy (asynchronous)
      const peerToPeerStrategy = {
        ...sampleStrategy,
        type: 'peer-to-peer' as const,
        communication: 'asynchronous' as const
      };

      const results = await manager.coordinateMultiRuleExecution(sampleRules, peerToPeerStrategy);
      expect(results).toHaveLength(2);

      // Test centralized strategy
      const centralizedStrategy = {
        ...sampleStrategy,
        type: 'centralized' as const
      };

      const results2 = await manager.coordinateMultiRuleExecution(sampleRules, centralizedStrategy);
      expect(results2).toHaveLength(2);
    });

    test('should handle rule execution failures with different failure handling strategies', async () => {
      await manager.initialize();

      // Mock rule execution to fail
      const originalExecuteRule = (manager as any)._executeRule;
      (manager as any)._executeRule = jest.fn()
        .mockResolvedValueOnce({
          executionId: 'exec-1',
          ruleId: 'rule-001',
          contextId: 'test',
          status: 'completed',
          timing: { startedAt: new Date(), endedAt: new Date(), durationMs: 100 },
          result: { success: true, data: {}, validations: [], actions: [] }
        })
        .mockResolvedValueOnce({
          executionId: 'exec-2',
          ruleId: 'rule-002',
          contextId: 'test',
          status: 'failed',
          timing: { startedAt: new Date(), endedAt: new Date(), durationMs: 100 },
          result: { success: false, data: {}, validations: [], actions: [] }
        });

      // Test fail-fast strategy
      const failFastStrategy = {
        ...sampleStrategy,
        failureHandling: 'fail-fast' as const
      };

      await expect(manager.coordinateMultiRuleExecution(sampleRules, failFastStrategy)).rejects.toThrow('Coordination failed');

      // Test graceful degradation strategy
      const gracefulStrategy = {
        ...sampleStrategy,
        failureHandling: 'graceful-degradation' as const
      };

      const results = await manager.coordinateMultiRuleExecution(sampleRules, gracefulStrategy);
      expect(results).toHaveLength(2);

      // Restore original method
      (manager as any)._executeRule = originalExecuteRule;
    });
  });

  // ============================================================================
  // RULE SET ORCHESTRATION TESTS
  // ============================================================================

  describe('Rule Set Orchestration', () => {
    const sampleRuleSets = [
      {
        ruleSetId: 'ruleset-001',
        name: 'Validation Rule Set',
        description: 'Set of validation rules',
        rules: [
          {
            ruleId: 'rule-001',
            name: 'Basic Validation',
            description: 'Basic validation rule',
            type: 'compliance-check' as const,
            category: 'governance',
            severity: 'warning' as const,
            priority: 1,
            configuration: {
              parameters: {},
              criteria: {
                type: 'condition' as const,
                expression: 'test.field === "test-value"',
                expectedValues: ['test-value'],
                operators: ['equals'],
                weight: 1.0
              },
              actions: [{
                type: 'log' as const,
                configuration: { message: 'validation action' },
                priority: 1
              }],
              dependencies: []
            },
            status: {
              current: 'active' as const,
              effectiveness: 1.0
            },
            metadata: {
              version: '1.0.0',
              author: 'test',
              createdAt: new Date(),
              modifiedAt: new Date(),
              tags: ['test'],
              documentation: []
            }
          }
        ],
        configuration: {
          executionOrder: 'sequential' as const,
          failureHandling: 'stop-on-first' as const,
          timeout: 30000,
          retryConfig: {
            maxAttempts: 3,
            delayMs: 1000,
            backoffStrategy: 'exponential' as const,
            maxDelayMs: 10000
          }
        },
        metadata: {
          version: '1.0.0',
          author: 'test',
          createdAt: new Date(),
          modifiedAt: new Date(),
          tags: ['validation']
        }
      },
      {
        ruleSetId: 'ruleset-002',
        name: 'Processing Rule Set',
        description: 'Set of processing rules',
        rules: [
          {
            ruleId: 'rule-002',
            name: 'Data Processing',
            description: 'Data processing rule',
            type: 'data-governance' as const,
            category: 'governance',
            severity: 'error' as const,
            priority: 2,
            configuration: {
              parameters: {},
              criteria: {
                type: 'condition' as const,
                expression: 'data.field === "data-value"',
                expectedValues: ['data-value'],
                operators: ['equals'],
                weight: 1.0
              },
              actions: [{
                type: 'alert' as const,
                configuration: { message: 'data processing action' },
                priority: 1
              }],
              dependencies: ['rule-001']
            },
            status: {
              current: 'active' as const,
              effectiveness: 1.0
            },
            metadata: {
              version: '1.0.0',
              author: 'test',
              createdAt: new Date(),
              modifiedAt: new Date(),
              tags: ['test'],
              documentation: []
            }
          }
        ],
        configuration: {
          executionOrder: 'parallel' as const,
          failureHandling: 'continue-on-failure' as const,
          timeout: 30000,
          retryConfig: {
            maxAttempts: 3,
            delayMs: 1000,
            backoffStrategy: 'linear' as const,
            maxDelayMs: 5000
          }
        },
        metadata: {
          version: '1.0.0',
          author: 'test',
          createdAt: new Date(),
          modifiedAt: new Date(),
          tags: ['processing']
        }
      }
    ];

    const sampleExecutionContext = {
      contextId: 'exec-context-001',
      name: 'Test Execution Context',
      ruleSetId: 'ruleset-001',
      environment: {
        environmentId: 'test-env',
        name: 'Test Environment',
        type: 'development' as const,
        configuration: {
          resourceLimits: {
            memory: 1024,
            cpu: 2,
            storage: 10240,
            networkBandwidth: 1000
          },
          security: {
            encryption: true,
            authentication: true,
            authorization: true,
            auditLogging: true
          },
          performance: {
            caching: true,
            compression: true,
            optimization: true
          }
        },
        variables: {},
        metadata: {
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date(),
          owner: 'test'
        }
      },
      state: {
        status: 'ready' as const,
        startedAt: new Date(),
        progress: 0,
        currentStep: 'initialization'
      },
      data: {
        input: {},
        output: {},
        intermediate: {},
        variables: {}
      },
      configuration: {
        timeout: 30000,
        errorHandling: 'best-effort' as const,
        loggingLevel: 'info' as const,
        monitoring: true
      },
      metadata: {
        version: '1.0.0',
        createdAt: new Date(),
        modifiedAt: new Date(),
        tags: ['test'],
        description: 'Test execution context'
      }
    };

    test('should orchestrate rule sets successfully', async () => {
      await manager.initialize();

      const result = await manager.orchestrateRuleSets(sampleRuleSets, sampleExecutionContext);

      expect(result).toBeDefined();
      expect(result.orchestrationId).toMatch(/ruleset-orchestration-\d+/);
      expect(result.status).toBe('COMPLETED');
      expect(result.executedSteps).toBeDefined();
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('rule_set_orchestration', expect.any(Object));
    });

    test('should handle circular dependencies in rule sets', async () => {
      await manager.initialize();

      const circularRuleSets = [
        {
          ...sampleRuleSets[0],
          rules: [
            {
              ...sampleRuleSets[0].rules[0],
              dependencies: ['rule-002'] // Creates circular dependency
            }
          ]
        },
        {
          ...sampleRuleSets[1],
          rules: [
            {
              ...sampleRuleSets[1].rules[0],
              dependencies: ['rule-001'] // Completes the circle
            }
          ]
        }
      ];

      await expect(manager.orchestrateRuleSets(circularRuleSets, sampleExecutionContext)).rejects.toThrow('Circular dependency detected');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('rule_set_orchestration', expect.any(Object));
    });

    test('should handle empty rule sets', async () => {
      await manager.initialize();

      const result = await manager.orchestrateRuleSets([], sampleExecutionContext);
      expect(result.executedSteps).toHaveLength(0);
    });

    test('should handle rule sets with complex dependencies', async () => {
      await manager.initialize();

      const complexRuleSets = [
        {
          ...sampleRuleSets[0],
          ruleSetId: 'complex-001',
          rules: [
            { ...sampleRuleSets[0].rules[0], ruleId: 'rule-A', dependencies: [] },
            { ...sampleRuleSets[0].rules[0], ruleId: 'rule-B', dependencies: ['rule-A'] },
            { ...sampleRuleSets[0].rules[0], ruleId: 'rule-C', dependencies: ['rule-A', 'rule-B'] }
          ]
        }
      ];

      const result = await manager.orchestrateRuleSets(complexRuleSets, sampleExecutionContext);
      expect(result.executedSteps).toHaveLength(3);
    });
  });

  // ============================================================================
  // WORKFLOW COORDINATION TESTS
  // ============================================================================

  describe('Workflow Coordination', () => {
    const sampleWorkflows = [
      {
        id: 'workflow-001',
        name: 'Primary Workflow',
        version: '1.0.0',
        description: 'Primary workflow for testing',
        steps: [
          {
            id: 'step-1',
            name: 'Initial Step',
            type: 'ACTION' as const,
            action: {
              type: 'service-call',
              service: 'test-service',
              method: 'execute',
              parameters: {}
            },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: {
              maxAttempts: 3,
              delay: 1000,
              backoffStrategy: 'exponential',
              maxDelay: 10000
            },
            validation: {
              enabled: true,
              rules: [],
              onFailure: 'stop'
            },
            parameters: {}
          }
        ],
        conditions: [],
        rollbackStrategy: 'AUTOMATIC' as const,
        timeout: 30000,
        priority: 'MEDIUM' as const,
        tags: ['test', 'primary'],
        metadata: {
          category: 'test',
          author: 'test-suite',
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      },
      {
        id: 'workflow-002',
        name: 'Secondary Workflow',
        version: '1.0.0',
        description: 'Secondary workflow for testing',
        steps: [
          {
            id: 'step-2',
            name: 'Dependent Step',
            type: 'CONDITION' as const,
            action: {
              type: 'condition',
              service: 'test-service',
              method: 'evaluate',
              parameters: { condition: 'workflow-001.completed' }
            },
            dependencies: ['workflow-001'],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: {
              maxAttempts: 2,
              delay: 2000,
              backoffStrategy: 'exponential',
              maxDelay: 10000
            },
            validation: {
              enabled: true,
              rules: [],
              onFailure: 'stop'
            },
            parameters: {}
          }
        ],
        conditions: [],
        rollbackStrategy: 'MANUAL' as const,
        timeout: 30000,
        priority: 'LOW' as const,
        tags: ['test', 'secondary'],
        metadata: {
          category: 'test',
          author: 'test-suite',
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      }
    ];

    test('should manage workflow coordination successfully', async () => {
      await manager.initialize();

      const results = await manager.manageWorkflowCoordination(sampleWorkflows);

      expect(results).toBeDefined();
      expect(results).toHaveLength(2);
      expect(results[0].workflowId).toBe('workflow-001');
      expect(results[1].workflowId).toBe('workflow-002');
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_coordination', expect.any(Object));
    });

    test('should handle workflow validation errors', async () => {
      await manager.initialize();

      const invalidWorkflows = [
        {
          ...sampleWorkflows[0],
          workflowId: '', // Invalid empty ID
          steps: [] // No steps
        }
      ];

      await expect(manager.manageWorkflowCoordination(invalidWorkflows)).rejects.toThrow();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_coordination', expect.any(Object));
    });

    test('should handle empty workflows array', async () => {
      await manager.initialize();

      const results = await manager.manageWorkflowCoordination([]);
      expect(results).toHaveLength(0);
    });

    test('should handle workflow dependencies correctly', async () => {
      await manager.initialize();

      // Mock workflow dependency analysis
      const originalAnalyzeDependencies = (manager as any)._analyzeWorkflowDependencies;
      (manager as any)._analyzeWorkflowDependencies = jest.fn().mockResolvedValue(
        new Map([
          ['workflow-001', []],
          ['workflow-002', ['workflow-001']]
        ])
      );

      const results = await manager.manageWorkflowCoordination(sampleWorkflows);
      expect(results).toHaveLength(2);

      // Restore original method
      (manager as any)._analyzeWorkflowDependencies = originalAnalyzeDependencies;
    });
  });

  // ============================================================================
  // METRICS AND MONITORING TESTS
  // ============================================================================

  describe('Metrics and Monitoring', () => {
    test('should get orchestration metrics successfully', async () => {
      await manager.initialize();

      const metrics = await manager.getOrchestrationMetrics();

      expect(metrics).toBeDefined();
      expect(metrics.orchestrationId).toMatch(/metrics-\d+/);
      expect(metrics.type).toBe('process-optimization');
      expect(metrics.services).toBeDefined();
      expect(metrics.coordinationStrategy).toBeDefined();
      expect(metrics.metrics).toBeDefined();
      expect(metrics.health).toBeDefined();
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('metrics_collection', expect.any(Object));
    });

    test('should handle metrics collection errors', async () => {
      await manager.initialize();

      // Mock metrics collection failure
      const originalCollectCurrentMetrics = (manager as any)._collectCurrentMetrics;
      (manager as any)._collectCurrentMetrics = jest.fn().mockRejectedValue(new Error('Metrics collection failed'));

      await expect(manager.getOrchestrationMetrics()).rejects.toThrow('Metrics collection failed');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('metrics_collection', expect.any(Object));

      // Restore original method
      (manager as any)._collectCurrentMetrics = originalCollectCurrentMetrics;
    });

    test('should update health status based on metrics', async () => {
      await manager.initialize();

      // Test healthy status
      (manager as any)._orchestrationMetrics.errorRate = 0.01;
      (manager as any)._orchestrationMetrics.resourceUtilization = { cpu: 50, memory: 512, network: 25 };
      await (manager as any)._updateHealthStatus();
      expect((manager as any)._healthStatus).toBe('healthy');

      // Test degraded status
      (manager as any)._orchestrationMetrics.errorRate = 0.07;
      await (manager as any)._updateHealthStatus();
      expect((manager as any)._healthStatus).toBe('degraded');

      // Test critical status
      (manager as any)._orchestrationMetrics.errorRate = 0.15;
      await (manager as any)._updateHealthStatus();
      expect((manager as any)._healthStatus).toBe('critical');
    });

    test('should perform health checks on services and orchestrations', async () => {
      await manager.initialize();

      // Add some active orchestrations for testing
      const mockOrchestration = {
        orchestrationId: 'test-orchestration',
        status: 'running' as const,
        startTime: new Date(Date.now() - 400000), // 6+ minutes ago
        endTime: undefined,
        activeWorkflows: new Map(),
        completedWorkflows: new Map(),
        failedWorkflows: new Map(),
        metrics: {
          totalWorkflows: 1,
          completedWorkflows: 0,
          failedWorkflows: 0,
          averageExecutionTime: 0,
          resourceUtilization: { cpu: 0, memory: 0, network: 0 },
          throughput: 0,
          errorRate: 0
        }
      };

      (manager as any)._activeOrchestrations.set('test-orchestration', mockOrchestration);

      await (manager as any)._performHealthCheck();

      // Verify health check was performed (no exceptions thrown)
      expect(true).toBe(true);
    });
  });

  // ============================================================================
  // MEMORY SAFETY AND RESILIENT TIMING TESTS
  // ============================================================================

  describe('Memory Safety and Resilient Timing', () => {
    test('should validate MEM-SAFE-002 compliance', async () => {
      // Verify dual-field resilient timing pattern (inheritance is mocked)
      expect((manager as any)._resilientTimer).toBeDefined();
      expect((manager as any)._metricsCollector).toBeDefined();

      // Verify manager has required methods from BaseTrackingService
      expect(typeof manager.initialize).toBe('function');
      expect(typeof manager.shutdown).toBe('function');
      expect(typeof (manager as any).logOperation).toBe('function');
      expect(typeof (manager as any).logError).toBe('function');
    });

    test('should handle resilient timing context creation and cleanup', async () => {
      await manager.initialize();

      // Test timing context creation
      expect(mockResilientTimer.start).toHaveBeenCalled();

      // Test metrics recording
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalled();

      // Test error handling with timing
      mockTimingContext.end.mockReturnValue(150);
      const config = {
        mode: 'invalid' as any,
        timeout: { workflow: -1, service: 0, coordination: 0 },
        retry: { maxAttempts: 0, backoffStrategy: 'invalid' as any, initialDelay: 0, maxDelay: 0 },
        monitoring: { enabled: true, interval: 5000, metrics: [], alerts: [] },
        security: { authentication: false, authorization: false, encryption: false, auditLogging: false },
        performance: {
          maxConcurrentWorkflows: 1,
          resourceLimits: {
            maxCpu: '10%',
            maxMemory: '100MB',
            maxStorage: '100MB',
            maxNetworkBandwidth: '10Mbps'
          },
          optimization: false
        }
      };

      await expect(manager.initializeOrchestration(config)).rejects.toThrow();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('orchestration_initialization', expect.any(Object));
    });

    test('should handle timing context errors gracefully', async () => {
      await manager.initialize();

      // Mock timing context to throw error
      mockTimingContext.end.mockImplementation(() => {
        throw new Error('Timing context error');
      });

      // Should still complete operation despite timing error
      const config = {
        mode: 'adaptive' as const,
        timeout: { workflow: 30000, service: 10000, coordination: 5000 },
        retry: { maxAttempts: 3, backoffStrategy: 'exponential' as const, initialDelay: 1000, maxDelay: 10000 },
        monitoring: { enabled: true, interval: 5000, metrics: [], alerts: [] },
        security: { authentication: true, authorization: true, encryption: true, auditLogging: true },
        performance: {
          maxConcurrentWorkflows: 10,
          resourceLimits: {
            maxCpu: '80%',
            maxMemory: '2GB',
            maxStorage: '1GB',
            maxNetworkBandwidth: '100Mbps'
          },
          optimization: true
        }
      };

      await expect(manager.initializeOrchestration(config)).resolves.not.toThrow();
    });

    test('should cleanup resources properly during shutdown', async () => {
      await manager.initialize();

      // Add some active orchestrations
      const mockState = {
        orchestrationId: 'test-cleanup',
        status: 'running' as const,
        startTime: new Date(),
        activeWorkflows: new Map(),
        completedWorkflows: new Map(),
        failedWorkflows: new Map(),
        metrics: {
          totalWorkflows: 0,
          completedWorkflows: 0,
          failedWorkflows: 0,
          averageExecutionTime: 0,
          resourceUtilization: { cpu: 0, memory: 0, network: 0 },
          throughput: 0,
          errorRate: 0
        }
      };

      (manager as any)._activeOrchestrations.set('test-cleanup', mockState);
      (manager as any)._workflowRegistry.set('test-workflow', {});
      (manager as any)._coordinationStrategies.set('test-strategy', {});
      (manager as any)._serviceRegistry.set('test-service', {});

      await manager.shutdown();

      // Verify cleanup
      expect((manager as any)._activeOrchestrations.size).toBe(0);
      expect((manager as any)._workflowRegistry.size).toBe(0);
      expect((manager as any)._coordinationStrategies.size).toBe(0);
      expect((manager as any)._serviceRegistry.size).toBe(0);
    });
  });

  // ============================================================================
  // SINGLETON FACTORY TESTS
  // ============================================================================

  describe('Singleton Factory', () => {
    test('should return same instance on multiple calls', () => {
      const instance1 = getGovernanceRuleOrchestrationManager();
      const instance2 = getGovernanceRuleOrchestrationManager();

      expect(instance1).toBe(instance2);
    });

    test('should create new instance with configuration', () => {
      // Reset singleton
      (getGovernanceRuleOrchestrationManager as any).orchestrationManagerInstance = null;

      const config = {
        service: {
          name: 'test-orchestration-manager',
          version: '1.0.0',
          environment: 'development' as const,
          timeout: 30000,
          retry: {
            maxAttempts: 3,
            delay: 1000,
            backoffMultiplier: 2,
            maxDelay: 10000
          }
        }
      };
      const instance = getGovernanceRuleOrchestrationManager(config);

      expect(instance).toBeDefined();
      expect(instance.id).toMatch(/governance-rule-orchestration-manager-\d+/);
    });
  });

  // ============================================================================
  // BRANCH COVERAGE ENHANCEMENT TESTS
  // ============================================================================

  describe('Branch Coverage Enhancement', () => {
    test('should test constructor test mode detection branches', () => {
      // Test Jest environment detection (Jest should be defined in test environment)
      expect(typeof jest).toBe('object');

      // Test with Jest defined (should be test mode)
      expect((manager as any)._testMode).toBe(true);

      // Test constructor with explicit test mode false but Jest present
      const managerWithExplicitFalse = new GovernanceRuleOrchestrationManager({
        service: { name: 'test', version: '1.0.0', environment: 'test' },
        _testMode: false
      } as any);
      expect((managerWithExplicitFalse as any)._testMode).toBe(true); // Should still be true due to Jest
    });

    test('should test doInitialize test mode branches', async () => {
      await manager.initialize();

      // Verify test mode skipped complex initialization
      expect((manager as any)._testMode).toBe(true);
      expect((manager as any)._isInitialized).toBe(true);

      // Test that monitoring setup was skipped in test mode
      expect((manager as any)._setupPerformanceMonitoring).toBeDefined();
      expect((manager as any)._startHealthMonitoring).toBeDefined();
    });

    test('should test doShutdown test mode branches', async () => {
      await manager.initialize();

      // Add some test data to verify cleanup
      (manager as any)._activeOrchestrations.set('test-1', {});
      (manager as any)._workflowRegistry.set('test-2', {});

      // Verify data was added
      expect((manager as any)._activeOrchestrations.size).toBe(1);
      expect((manager as any)._workflowRegistry.size).toBe(1);

      await manager.shutdown();

      // Verify test mode immediate cleanup
      expect((manager as any)._activeOrchestrations.size).toBe(0);
      expect((manager as any)._workflowRegistry.size).toBe(0);
      expect((manager as any)._isInitialized).toBe(false);
    });

    test('should test resilient timing cleanup branches', async () => {
      // Create a production mode manager to test actual cleanup paths
      const prodManager = new GovernanceRuleOrchestrationManager({
        service: { name: 'test', version: '1.0.0', environment: 'production' },
        _testMode: false
      } as any);

      // Mock the resilient timing components with cleanup methods
      const mockTimerWithCleanup = {
        start: () => ({ end: () => ({ duration: 1 }), getDuration: () => 1 }),
        cleanup: jest.fn().mockResolvedValue(undefined),
        isHealthy: () => true,
        getMetrics: () => ({})
      };
      const mockCollectorWithCleanup = {
        recordTiming: jest.fn(),
        cleanup: jest.fn().mockResolvedValue(undefined),
        isHealthy: () => true,
        getMetrics: () => ({})
      };

      (prodManager as any)._resilientTimer = mockTimerWithCleanup;
      (prodManager as any)._metricsCollector = mockCollectorWithCleanup;

      await prodManager.initialize();
      await prodManager.shutdown();

      // Verify cleanup was called
      expect(mockTimerWithCleanup.cleanup).toHaveBeenCalled();
      expect(mockCollectorWithCleanup.cleanup).toHaveBeenCalled();
    });

    test('should test resilient timing cleanup error branches', async () => {
      // Create a production mode manager to test actual cleanup error paths
      const prodManager = new GovernanceRuleOrchestrationManager({
        service: { name: 'test', version: '1.0.0', environment: 'production' },
        _testMode: false
      } as any);

      // Test cleanup with error throwing
      const mockTimerWithError = {
        start: () => ({ end: () => ({ duration: 1 }), getDuration: () => 1 }),
        cleanup: jest.fn().mockRejectedValue(new Error('Cleanup failed')),
        isHealthy: () => true,
        getMetrics: () => ({})
      };
      const mockCollectorNormal = {
        recordTiming: jest.fn(),
        cleanup: jest.fn().mockResolvedValue(undefined),
        isHealthy: () => true,
        getMetrics: () => ({})
      };

      (prodManager as any)._resilientTimer = mockTimerWithError;
      (prodManager as any)._metricsCollector = mockCollectorNormal;

      await prodManager.initialize();

      // Should not throw, just log error
      await expect(prodManager.shutdown()).resolves.not.toThrow();
      expect(mockTimerWithError.cleanup).toHaveBeenCalled();
    });

    test('should test monitoring setup branches in test mode', async () => {
      // Test that monitoring methods exist and can be called
      expect(typeof (manager as any)._setupPerformanceMonitoring).toBe('function');
      expect(typeof (manager as any)._startHealthMonitoring).toBe('function');

      // Test direct method calls to verify they work in test mode
      await (manager as any)._setupPerformanceMonitoring();
      await (manager as any)._startHealthMonitoring();

      // Verify test mode behavior - methods should complete without creating real timers
      expect((manager as any)._testMode).toBe(true);
    });

    test('should test orchestration configuration validation branches', async () => {
      await manager.initialize();

      // Test valid configuration branches
      const validConfig = {
        mode: 'sequential' as const,
        priority: 'high' as const,
        timeout: {
          workflow: 30000,
          service: 10000,
          coordination: 5000
        },
        retryPolicy: {
          maxAttempts: 3,
          delay: 1000,
          backoffStrategy: 'exponential' as const,
          maxDelay: 10000
        },
        retry: {
          maxAttempts: 3,
          backoffStrategy: 'exponential' as const,
          initialDelay: 1000,
          maxDelay: 10000
        },
        monitoring: {
          enabled: true,
          interval: 30000,
          metrics: ['execution_time', 'success_rate', 'error_rate'],
          alerts: []
        },
        security: {
          authentication: false,
          authorization: false,
          encryption: false,
          auditLogging: false
        },
        performance: {
          maxConcurrentWorkflows: 10,
          resourceLimits: {
            maxConcurrentRules: 10,
            maxMemoryUsage: 512 * 1024 * 1024,
            maxExecutionTime: 300000,
            maxCpu: '80%',
            maxMemory: '512MB',
            maxStorage: '1GB',
            maxNetworkBandwidth: '100Mbps'
          },
          optimization: true
        }
      };

      await expect(manager.initializeOrchestration(validConfig)).resolves.not.toThrow();

      // Test invalid mode branch - should validate even in test mode
      const invalidModeConfig = { ...validConfig, mode: 'INVALID' as any };
      await expect(manager.initializeOrchestration(invalidModeConfig)).rejects.toThrow('Invalid orchestration mode');

      // Test invalid priority branch - test with a completely invalid structure
      const invalidPriorityConfig = { ...validConfig, priority: undefined as any };
      await expect(manager.initializeOrchestration(invalidPriorityConfig)).rejects.toThrow();

      // Test negative timeout branch
      const negativeTimeoutConfig = {
        ...validConfig,
        timeout: { workflow: -1000, service: -500, coordination: -250 }
      };
      await expect(manager.initializeOrchestration(negativeTimeoutConfig)).rejects.toThrow();
    });

    test('should test workflow validation edge cases', async () => {
      await manager.initialize();

      // Test empty workflow
      const emptyWorkflow = {
        id: 'empty-workflow',
        name: 'Empty Workflow',
        version: '1.0.0',
        description: 'Empty workflow for testing',
        steps: [],
        conditions: [],
        rollbackStrategy: 'MANUAL' as const,
        timeout: 1000,
        priority: 'LOW' as const,
        tags: [],
        metadata: {
          category: 'test',
          author: 'test',
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      };

      const sampleContext = {
        contextId: 'test-context-id',
        userId: 'test-user',
        sessionId: 'test-session',
        requestId: 'test-request',
        timestamp: new Date(),
        environment: 'development' as const,
        technical: {
          version: '1.0.0',
          features: ['orchestration', 'workflow'],
          capabilities: ['sequential', 'parallel']
        },
        custom: {},
        metadata: {}
      };

      // Empty workflow should be rejected
      await expect(manager.executeWorkflow(emptyWorkflow, sampleContext)).rejects.toThrow('Workflow must have at least one step');

      // Test workflow with invalid step type
      const invalidStepWorkflow = {
        ...emptyWorkflow,
        steps: [{
          id: 'invalid-step',
          name: 'Invalid Step',
          type: 'INVALID' as any,
          action: { type: 'test', service: 'test', method: 'test', parameters: {} },
          dependencies: [],
          timeout: 100,
          retryPolicy: { maxAttempts: 1, delay: 10, backoffStrategy: 'linear' as const, maxDelay: 50 },
          validation: { enabled: false, rules: [], onFailure: 'continue' as const },
          parameters: {}
        }]
      };

      await expect(manager.executeWorkflow(invalidStepWorkflow, sampleContext)).rejects.toThrow();
    });

    test('should test private method branches with surgical precision', async () => {
      await manager.initialize();

      // Test _validateOrchestrationConfig with various edge cases
      const validateConfig = (manager as any)._validateOrchestrationConfig.bind(manager);

      // Test null/undefined config - these should return invalid results
      const nullResult = await validateConfig(null);
      expect(nullResult.status).toBe('invalid');
      expect(nullResult.errors.length).toBeGreaterThan(0);

      const undefinedResult = await validateConfig(undefined);
      expect(undefinedResult.status).toBe('invalid');
      expect(undefinedResult.errors.length).toBeGreaterThan(0);

      // Test missing required fields
      expect(() => validateConfig({})).toThrow();
      expect(() => validateConfig({ mode: 'sequential' })).toThrow();

      // Test invalid enum values
      expect(() => validateConfig({
        mode: 'invalid-mode',
        timeout: { workflow: 1000, service: 500, coordination: 250 }
      })).toThrow();
    });

    test('should test workflow step execution branches', async () => {
      await manager.initialize();

      // Test _executeStep with different step types
      const executeStep = (manager as any)._executeStep.bind(manager);
      const sampleContext = {
        contextId: 'test-context',
        userId: 'test-user',
        sessionId: 'test-session',
        requestId: 'test-request',
        timestamp: new Date(),
        environment: 'development' as const,
        technical: { version: '1.0.0', features: [], capabilities: [] },
        custom: {},
        metadata: {}
      };

      // Test ACTION step type
      const actionStep = {
        id: 'action-step',
        name: 'Action Step',
        type: 'ACTION' as const,
        action: { type: 'test', service: 'test', method: 'test', parameters: {} },
        dependencies: [],
        timeout: 1000,
        retryPolicy: { maxAttempts: 1, delay: 100, backoffStrategy: 'linear' as const, maxDelay: 500 },
        validation: { enabled: false, rules: [], onFailure: 'continue' as const },
        parameters: {}
      };

      await expect(executeStep(actionStep, sampleContext)).resolves.toBeDefined();

      // Test CONDITION step type
      const conditionStep = {
        ...actionStep,
        id: 'condition-step',
        type: 'CONDITION' as const
      };

      await expect(executeStep(conditionStep, sampleContext)).resolves.toBeDefined();

      // Test WAIT step type
      const waitStep = {
        ...actionStep,
        id: 'wait-step',
        type: 'WAIT' as const,
        action: { ...actionStep.action, parameters: { duration: 10 } }
      };

      await expect(executeStep(waitStep, sampleContext)).resolves.toBeDefined();
    });

    test('should test error handling branches with strategic error injection', async () => {
      await manager.initialize();

      // Test error handling in orchestration initialization
      const originalValidate = (manager as any)._validateOrchestrationConfig;
      (manager as any)._validateOrchestrationConfig = jest.fn().mockImplementation(() => {
        throw new Error('Validation error');
      });

      const config = {
        mode: 'sequential' as const,
        timeout: { workflow: 1000, service: 500, coordination: 250 },
        retry: { maxAttempts: 1, backoffStrategy: 'linear' as const, initialDelay: 100, maxDelay: 500 },
        monitoring: { enabled: false, interval: 1000, metrics: [], alerts: [] },
        security: { authentication: false, authorization: false, encryption: false, auditLogging: false },
        performance: {
          maxConcurrentWorkflows: 1,
          resourceLimits: {
            maxConcurrentRules: 1,
            maxMemoryUsage: 1024,
            maxExecutionTime: 1000,
            maxCpu: '10%',
            maxMemory: '100MB',
            maxStorage: '100MB',
            maxNetworkBandwidth: '10Mbps'
          },
          optimization: false
        }
      };

      await expect(manager.initializeOrchestration(config)).rejects.toThrow('Validation error');

      // Restore original method
      (manager as any)._validateOrchestrationConfig = originalValidate;
    });
  });

  // ============================================================================
  // SURGICAL PRECISION COVERAGE TESTS
  // ============================================================================

  describe('Surgical Precision Coverage', () => {
    test('should test conditional branches in private methods', async () => {
      await manager.initialize();

      // Test _calculateResourceUsage with edge cases
      const calculateResource = (manager as any)._calculateResourceUsage.bind(manager);

      // Test with different input scenarios
      const result1 = await calculateResource('test-orchestration-1');
      expect(result1).toBeDefined();
      expect(typeof result1.cpu).toBe('number');
      expect(typeof result1.memory).toBe('number');
      expect(typeof result1.network).toBe('number');
      expect(typeof result1.storage).toBe('number');

      // Test multiple calls to verify randomization branches
      const result2 = await calculateResource('test-orchestration-2');
      expect(result2).toBeDefined();

      // Results should be different due to randomization
      expect(result1.cpu !== result2.cpu || result1.memory !== result2.memory).toBe(true);
    });

    test('should test ternary operator branches', async () => {
      await manager.initialize();

      // Test _getOrchestrationHealth with different health statuses
      const getHealth = (manager as any)._getOrchestrationHealth.bind(manager);

      // Test with healthy status
      (manager as any)._healthStatus = 'healthy';
      const healthyResult = await getHealth();
      expect(healthyResult.score).toBe(100);

      // Test with degraded status
      (manager as any)._healthStatus = 'degraded';
      const degradedResult = await getHealth();
      expect(degradedResult.score).toBe(70);

      // Test with unhealthy status
      (manager as any)._healthStatus = 'unhealthy';
      const unhealthyResult = await getHealth();
      expect(unhealthyResult.score).toBe(30);

      // Test with unknown status (should default to 30)
      (manager as any)._healthStatus = 'unknown';
      const unknownResult = await getHealth();
      expect(unknownResult.score).toBe(30);
    });

    test('should test logical operator branches (&&, ||)', async () => {
      await manager.initialize();

      // Test _updateHealthStatus method (it exists and uses logical operators)
      const updateHealth = (manager as any)._updateHealthStatus.bind(manager);

      // Set up different error rates to test logical branches
      (manager as any)._orchestrationMetrics.errorRate = 0.05; // Low error rate
      (manager as any)._orchestrationMetrics.resourceUtilization = { cpu: 30, memory: 200 };

      await updateHealth();
      expect((manager as any)._healthStatus).toBe('healthy');

      // Test with high error rate (should trigger unhealthy)
      (manager as any)._orchestrationMetrics.errorRate = 0.25; // High error rate
      (manager as any)._orchestrationMetrics.resourceUtilization = { cpu: 90, memory: 900 };

      await updateHealth();
      expect((manager as any)._healthStatus).toBe('critical');

      // Test with moderate error rate (should trigger degraded)
      // errorRate > 0.05 but <= 0.1 should be degraded
      (manager as any)._orchestrationMetrics.errorRate = 0.08; // Between 0.05 and 0.1
      (manager as any)._orchestrationMetrics.resourceUtilization = { cpu: 70, memory: 600 }; // 70/100=0.7, 600/1024=0.58

      await updateHealth();
      expect((manager as any)._healthStatus).toBe('degraded');
    });

    test('should test switch statement branches', async () => {
      await manager.initialize();

      // Test switch statement in _collectConfigBasedMetrics method instead
      const collectMetrics = (manager as any)._collectConfigBasedMetrics.bind(manager);

      // Test different metric types to hit switch cases
      const config = {
        monitoring: {
          metrics: ['execution_time', 'success_rate', 'error_rate', 'unknown_metric']
        }
      };

      // This should test the switch statement without causing timeouts
      await expect(collectMetrics(config)).resolves.not.toThrow();

      // Test _executeStep with simpler approach - just verify the method exists and can handle basic cases
      const executeStep = (manager as any)._executeStep.bind(manager);
      const sampleContext = {
        contextId: 'test-context',
        userId: 'test-user',
        sessionId: 'test-session',
        requestId: 'test-request',
        timestamp: new Date(),
        environment: 'development' as const,
        technical: { version: '1.0.0', features: [], capabilities: [] },
        custom: {},
        metadata: {}
      };

      // Test CONDITION step type (simplest, no external dependencies)
      const conditionStep = {
        id: 'condition-step',
        name: 'Condition Step',
        type: 'CONDITION' as const,
        action: { type: 'test', service: 'test', method: 'test', parameters: {} },
        dependencies: [],
        timeout: 1000,
        retryPolicy: { maxAttempts: 1, delay: 100, backoffStrategy: 'linear' as const, maxDelay: 500 },
        validation: { enabled: false, rules: [], onFailure: 'continue' as const },
        parameters: {}
      };

      await expect(executeStep(conditionStep, sampleContext)).resolves.not.toThrow();

      // Test unknown step type (should hit default case and call _executeCustomStep)
      const unknownStep = { ...conditionStep, type: 'UNKNOWN_TYPE' as any };
      await expect(executeStep(unknownStep, sampleContext)).resolves.not.toThrow();
    });

    test('should test error instanceof branches', async () => {
      await manager.initialize();

      // Test _handleWorkflowFailure with different error types
      const handleFailure = (manager as any)._handleWorkflowFailure.bind(manager);

      const sampleWorkflow = {
        id: 'test-workflow',
        name: 'Test Workflow',
        version: '1.0.0',
        description: 'Test workflow',
        steps: [],
        conditions: [],
        rollbackStrategy: 'MANUAL' as const,
        timeout: 1000,
        priority: 'LOW' as const,
        tags: [],
        metadata: {
          category: 'test',
          author: 'test',
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      };

      // Test with Error instance
      const standardError = new Error('Standard error');
      await expect(handleFailure('test-orchestration-1', sampleWorkflow, standardError)).resolves.not.toThrow();

      // Test with non-Error object (string)
      const stringError = 'String error';
      await expect(handleFailure('test-orchestration-2', sampleWorkflow, stringError)).resolves.not.toThrow();

      // Test with object error
      const objectError = { message: 'Object error', code: 500 };
      await expect(handleFailure('test-orchestration-3', sampleWorkflow, objectError)).resolves.not.toThrow();

      // Test with null error
      await expect(handleFailure('test-orchestration-4', sampleWorkflow, null)).resolves.not.toThrow();
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    test('should handle initialization failure gracefully', async () => {
      const originalDoInitialize = (manager as any).doInitialize;
      (manager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Initialization failed'));

      await expect(manager.initialize()).rejects.toThrow('Initialization failed');

      // Restore original method
      (manager as any).doInitialize = originalDoInitialize;
    });

    test('should handle shutdown errors gracefully', async () => {
      await manager.initialize();

      const originalCancelActiveOrchestrations = (manager as any)._cancelActiveOrchestrations;
      (manager as any)._cancelActiveOrchestrations = jest.fn().mockRejectedValue(new Error('Shutdown error'));

      await expect(manager.shutdown()).rejects.toThrow('Shutdown error');

      // Restore original method
      (manager as any)._cancelActiveOrchestrations = originalCancelActiveOrchestrations;
    });

    test('should handle workflow failure scenarios', async () => {
      await manager.initialize();

      const workflow = {
        id: 'failing-workflow',
        name: 'Failing Workflow',
        version: '1.0.0',
        description: 'Workflow that will fail',
        steps: [
          {
            id: 'failing-step',
            name: 'Failing Step',
            type: 'ACTION' as const,
            action: {
              type: 'service-call',
              service: 'non-existent-service',
              method: 'execute',
              parameters: {}
            },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: {
              maxAttempts: 1,
              delay: 100,
              backoffStrategy: 'exponential',
              maxDelay: 1000
            },
            validation: {
              enabled: true,
              rules: [],
              onFailure: 'stop'
            },
            parameters: {}
          }
        ],
        conditions: [],
        rollbackStrategy: 'AUTOMATIC' as const,
        timeout: 30000,
        priority: 'HIGH' as const,
        tags: ['test', 'failing'],
        metadata: {
          category: 'test',
          author: 'test-suite',
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      };

      const context = {
        contextId: 'failing-context',
        user: {
          id: 'test-user',
          roles: ['test'],
          permissions: ['test:read', 'test:write']
        },
        environment: 'development' as const,
        technical: {
          version: '1.0.0',
          features: ['testing'],
          capabilities: ['workflow-execution']
        },
        custom: {
          testMode: true,
          source: 'unit-test'
        }
      };

      // Mock service call to fail
      const originalExecuteServiceCall = (manager as any)._executeServiceCall;
      (manager as any)._executeServiceCall = jest.fn().mockRejectedValue(new Error('Service not found'));

      await expect(manager.executeWorkflow(workflow, context)).rejects.toThrow();

      // Restore original method
      (manager as any)._executeServiceCall = originalExecuteServiceCall;
    });

    test('should handle condition evaluation edge cases', async () => {
      await manager.initialize();

      // Test condition evaluation with various scenarios
      const testCondition = { name: 'test-condition', expression: 'value === true', operator: 'equals' as const, value: true };
      const testContext = {
        contextId: 'test',
        userId: 'test',
        sessionId: 'test',
        requestId: 'test',
        timestamp: new Date(),
        environment: 'test',
        metadata: {},
        errorHandling: { onError: 'continue' as const, maxRetries: 3, retryDelay: 1000, rollbackSteps: [] }
      };

      const result = await (manager as any)._evaluateCondition(testCondition, testContext);
      expect(typeof result).toBe('boolean');
    });

    test('should handle resource calculation edge cases', async () => {
      await manager.initialize();

      const resourceUsage = await (manager as any)._calculateResourceUsage('test-orchestration');
      expect(resourceUsage).toBeDefined();
      expect(typeof resourceUsage.cpu).toBe('number');
      expect(typeof resourceUsage.memory).toBe('number');
      expect(typeof resourceUsage.network).toBe('number');
    });

    test('should handle metrics collection errors', async () => {
      await manager.initialize();

      // Mock performance metrics collection to fail
      const originalCollectPerformanceMetrics = (manager as any)._collectPerformanceMetrics;
      (manager as any)._collectPerformanceMetrics = jest.fn().mockRejectedValue(new Error('Metrics error'));

      // Should not throw error, just log it
      await (manager as any)._collectPerformanceMetrics();

      // Restore original method
      (manager as any)._collectPerformanceMetrics = originalCollectPerformanceMetrics;
    });
  });
});
